/**
 * settings.ts
 * 
 * This script handles the settings page logic for managing AI models,
 * downloading models, tracking progress, and managing storage.
 */

interface ModelInfo {
    model_id: string;
    model: string;
    model_lib: string;
    vram_required_MB: number;
    low_resource_required: boolean;
    parameters: string;
    description: string;
    recommended_for: string[];
}

interface ModelStatus {
    downloaded: boolean;
    downloading: boolean;
    current: boolean;
    downloadProgress?: number;
    size?: number;
}

interface StorageInfo {
    totalSize: number;
    modelsCount: number;
    currentModel: string;
}

// DOM elements
const modelsLoading = document.getElementById('models-loading') as HTMLDivElement;
const modelsError = document.getElementById('models-error') as HTMLDivElement;
const modelsGrid = document.getElementById('models-grid') as HTMLDivElement;
const totalStorageEl = document.getElementById('total-storage') as HTMLDivElement;
const modelsCountEl = document.getElementById('models-count') as HTMLDivElement;
const currentModelEl = document.getElementById('current-model') as HTMLDivElement;
const refreshStorageBtn = document.getElementById('refresh-storage') as HTMLButtonElement;
const clearCacheBtn = document.getElementById('clear-cache') as HTMLButtonElement;

// Available models with enhanced information
const availableModels: ModelInfo[] = [
    {
        model_id: "Llama-3.2-1B-Instruct-q4f32_1-MLC",
        model: "https://huggingface.co/mlc-ai/Llama-3.2-1B-Instruct-q4f32_1-MLC",
        model_lib: "Llama-3.2-1B-Instruct-q4f32_1-ctx4k_cs1k-webgpu.wasm",
        vram_required_MB: 1128.82,
        low_resource_required: true,
        parameters: "1B",
        description: "Fastest model, good for basic classification. May have lower accuracy on complex tasks.",
        recommended_for: ["Low-end devices", "Quick testing", "Basic classification"]
    },
    {
        model_id: "Llama-3.2-3B-Instruct-q4f32_1-MLC",
        model: "https://huggingface.co/mlc-ai/Llama-3.2-3B-Instruct-q4f32_1-MLC",
        model_lib: "Llama-3.2-3B-Instruct-q4f32_1-ctx4k_cs1k-webgpu.wasm",
        vram_required_MB: 2951.51,
        low_resource_required: true,
        parameters: "3B",
        description: "Balanced performance and speed. Recommended for most users. Good accuracy with reasonable resource usage.",
        recommended_for: ["Most users", "Balanced performance", "Good accuracy"]
    },
    {
        model_id: "Qwen2.5-7B-Instruct-q4f32_1-MLC",
        model: "https://huggingface.co/mlc-ai/Qwen2.5-7B-Instruct-q4f32_1-MLC",
        model_lib: "Qwen2-7B-Instruct-q4f32_1-ctx4k_cs1k-webgpu.wasm",
        vram_required_MB: 5900.09,
        low_resource_required: false,
        parameters: "7B",
        description: "High accuracy model, excellent for classification tasks. Requires more resources but provides superior results.",
        recommended_for: ["High accuracy", "Complex classification", "Powerful devices"]
    },
    {
        model_id: "Llama-3.1-8B-Instruct-q4f32_1-MLC",
        model: "https://huggingface.co/mlc-ai/Llama-3.1-8B-Instruct-q4f32_1-MLC",
        model_lib: "Llama-3_1-8B-Instruct-q4f32_1-ctx4k_cs1k-webgpu.wasm",
        vram_required_MB: 6101.01,
        low_resource_required: false,
        parameters: "8B",
        description: "Best accuracy available. Ideal for users who want the highest quality classification results.",
        recommended_for: ["Best accuracy", "Professional use", "High-end devices"]
    },
    {
        model_id: "Hermes-3-Llama-3.1-8B-q4f32_1-MLC",
        model: "https://huggingface.co/mlc-ai/Hermes-3-Llama-3.1-8B-q4f32_1-MLC",
        model_lib: "Llama-3_1-8B-Instruct-q4f32_1-ctx4k_cs1k-webgpu.wasm",
        vram_required_MB: 5779.27,
        low_resource_required: false,
        parameters: "8B",
        description: "Fine-tuned for instruction following. Excellent for understanding complex classification requirements.",
        recommended_for: ["Instruction following", "Complex tasks", "Advanced users"]
    }
];

let modelStatuses: Map<string, ModelStatus> = new Map();
let currentSelectedModel = "";

// Initialize the settings page
document.addEventListener('DOMContentLoaded', async () => {
    await initializeSettingsTheme();
    await loadSettings();
    setupSettingsEventListeners();
});

// Theme management (similar to bookmarks.ts)
async function initializeSettingsTheme() {
    try {
        const result = await chrome.storage.local.get(['theme']);
        const savedTheme = result.theme as 'light' | 'dark' | 'system' || 'system';
        applySettingsTheme(savedTheme);

        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            mediaQuery.addEventListener('change', () => {
                if (savedTheme === 'system') {
                    applySettingsTheme('system');
                }
            });
        }
    } catch (error) {
        console.error('Error initializing theme:', error);
        applySettingsTheme('system');
    }
}

function applySettingsTheme(theme: 'light' | 'dark' | 'system') {
    const body = document.body;
    body.removeAttribute('data-theme');
    
    if (theme === 'dark') {
        body.setAttribute('data-theme', 'dark');
    } else if (theme === 'system') {
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            body.setAttribute('data-theme', 'dark');
        }
    }
}

async function loadSettings() {
    try {
        // Load current model selection
        const result = await chrome.storage.local.get(['selectedModel']);
        currentSelectedModel = result.selectedModel || "Llama-3.2-3B-Instruct-q4f32_1-MLC";
        
        // Load storage information
        await updateStorageInfo();
        
        // Load model statuses
        await updateModelStatuses();
        
        // Render models
        renderModels();
        
        modelsLoading.style.display = 'none';
        modelsGrid.style.display = 'grid';
        
    } catch (error) {
        console.error('Error loading settings:', error);
        showError('Failed to load settings. Please refresh the page.');
    }
}

function setupSettingsEventListeners() {
    refreshStorageBtn.addEventListener('click', async () => {
        refreshStorageBtn.textContent = '🔄 Refreshing...';
        refreshStorageBtn.disabled = true;
        
        await updateStorageInfo();
        await updateModelStatuses();
        renderModels();
        
        refreshStorageBtn.textContent = '🔄 Refresh';
        refreshStorageBtn.disabled = false;
    });
    
    clearCacheBtn.addEventListener('click', async () => {
        if (confirm('Are you sure you want to clear all cached models? This will free up storage but you\'ll need to re-download models when needed.')) {
            await clearAllCache();
        }
    });
}

async function updateStorageInfo() {
    try {
        // Request storage info from background script
        const response = await chrome.runtime.sendMessage({ 
            action: 'getStorageInfo' 
        });
        
        if (response.success) {
            const info: StorageInfo = response.data;
            totalStorageEl.textContent = formatBytes(info.totalSize);
            modelsCountEl.textContent = info.modelsCount.toString();
            currentModelEl.textContent = getModelDisplayName(info.currentModel);
        }
    } catch (error) {
        console.error('Error updating storage info:', error);
        totalStorageEl.textContent = 'Error';
        modelsCountEl.textContent = 'Error';
        currentModelEl.textContent = 'Error';
    }
}

async function updateModelStatuses() {
    try {
        // Request model statuses from background script
        const response = await chrome.runtime.sendMessage({ 
            action: 'getModelStatuses' 
        });
        
        if (response.success) {
            modelStatuses = new Map(Object.entries(response.data));
        }
    } catch (error) {
        console.error('Error updating model statuses:', error);
    }
}

function renderModels() {
    modelsGrid.innerHTML = '';
    
    availableModels.forEach(model => {
        const status = modelStatuses.get(model.model_id) || {
            downloaded: false,
            downloading: false,
            current: false
        };
        
        const modelCard = createModelCard(model, status);
        modelsGrid.appendChild(modelCard);
    });
}

function createModelCard(model: ModelInfo, status: ModelStatus): HTMLElement {
    const card = document.createElement('div');
    card.className = `model-card ${status.current ? 'selected' : ''} ${status.downloading ? 'downloading' : ''}`;
    
    const statusBadge = status.current ? 'status-current' : 
                       status.downloaded ? 'status-downloaded' : 
                       status.downloading ? 'status-downloading' : '';
    
    const statusText = status.current ? 'Current' : 
                      status.downloaded ? 'Downloaded' : 
                      status.downloading ? 'Downloading' : '';
    
    card.innerHTML = `
        ${statusBadge ? `<div class="status-badge ${statusBadge}">${statusText}</div>` : ''}
        <div class="model-header">
            <div>
                <div class="model-name">${getModelDisplayName(model.model_id)}</div>
                <div class="model-size">${formatBytes(model.vram_required_MB * 1024 * 1024)} VRAM</div>
            </div>
        </div>
        <div class="model-specs">
            <div class="spec-item">
                <span class="spec-label">Parameters:</span>
                <span class="spec-value">${model.parameters}</span>
            </div>
            <div class="spec-item">
                <span class="spec-label">Resource:</span>
                <span class="spec-value">${model.low_resource_required ? 'Low' : 'High'}</span>
            </div>
        </div>
        <div class="model-description">${model.description}</div>
        <div class="recommended-tags">
            ${model.recommended_for.map(tag => `<span class="tag">${tag}</span>`).join('')}
        </div>
        <div class="model-actions">
            ${renderModelActions(model, status)}
        </div>
        <div class="progress-container ${status.downloading ? 'visible' : ''}" id="progress-${model.model_id}">
            <div class="progress-bar">
                <div class="progress-fill" style="width: ${status.downloadProgress || 0}%"></div>
            </div>
            <div class="progress-text">${status.downloadProgress || 0}% downloaded</div>
        </div>
    `;
    
    return card;
}

function renderModelActions(model: ModelInfo, status: ModelStatus): string {
    if (status.downloading) {
        return `<button class="model-btn btn-secondary" disabled>⏳ Downloading...</button>`;
    }
    
    if (status.current) {
        return `
            <button class="model-btn btn-success" disabled>✅ Current Model</button>
            <button class="model-btn btn-danger" onclick="removeModel('${model.model_id}')">🗑️ Remove</button>
        `;
    }
    
    if (status.downloaded) {
        return `
            <button class="model-btn btn-primary" onclick="selectModel('${model.model_id}')">🎯 Use This Model</button>
            <button class="model-btn btn-danger" onclick="removeModel('${model.model_id}')">🗑️ Remove</button>
        `;
    }
    
    return `<button class="model-btn btn-primary" onclick="downloadModel('${model.model_id}')">⬇️ Download</button>`;
}

// Global functions for button clicks
(window as any).downloadModel = async function(modelId: string) {
    try {
        const response = await chrome.runtime.sendMessage({
            action: 'downloadModel',
            modelId: modelId
        });
        
        if (response.success) {
            // Update UI to show downloading state
            const status = modelStatuses.get(modelId) || { downloaded: false, downloading: false, current: false };
            status.downloading = true;
            status.downloadProgress = 0;
            modelStatuses.set(modelId, status);
            renderModels();
            
            // Start progress tracking
            trackDownloadProgress(modelId);
        } else {
            showError(`Failed to start download: ${response.error}`);
        }
    } catch (error) {
        console.error('Error downloading model:', error);
        showError('Failed to download model. Please try again.');
    }
};

(window as any).selectModel = async function(modelId: string) {
    try {
        const response = await chrome.runtime.sendMessage({
            action: 'selectModel',
            modelId: modelId
        });
        
        if (response.success) {
            currentSelectedModel = modelId;
            await updateModelStatuses();
            renderModels();
        } else {
            showError(`Failed to select model: ${response.error}`);
        }
    } catch (error) {
        console.error('Error selecting model:', error);
        showError('Failed to select model. Please try again.');
    }
};

(window as any).removeModel = async function(modelId: string) {
    if (!confirm(`Are you sure you want to remove ${getModelDisplayName(modelId)}? You'll need to re-download it if you want to use it again.`)) {
        return;
    }
    
    try {
        const response = await chrome.runtime.sendMessage({
            action: 'removeModel',
            modelId: modelId
        });
        
        if (response.success) {
            await updateStorageInfo();
            await updateModelStatuses();
            renderModels();
        } else {
            showError(`Failed to remove model: ${response.error}`);
        }
    } catch (error) {
        console.error('Error removing model:', error);
        showError('Failed to remove model. Please try again.');
    }
};

async function trackDownloadProgress(modelId: string) {
    const interval = setInterval(async () => {
        try {
            const response = await chrome.runtime.sendMessage({
                action: 'getDownloadProgress',
                modelId: modelId
            });
            
            if (response.success) {
                const progress = response.progress;
                const status = modelStatuses.get(modelId);
                
                if (status) {
                    status.downloadProgress = progress;
                    
                    if (progress >= 100) {
                        status.downloading = false;
                        status.downloaded = true;
                        clearInterval(interval);
                        await updateStorageInfo();
                    }
                    
                    modelStatuses.set(modelId, status);
                    updateProgressUI(modelId, progress);
                }
            }
        } catch (error) {
            console.error('Error tracking download progress:', error);
            clearInterval(interval);
        }
    }, 1000);
}

function updateProgressUI(modelId: string, progress: number) {
    const progressContainer = document.getElementById(`progress-${modelId}`);
    if (progressContainer) {
        const progressFill = progressContainer.querySelector('.progress-fill') as HTMLElement;
        const progressText = progressContainer.querySelector('.progress-text') as HTMLElement;
        
        if (progressFill) progressFill.style.width = `${progress}%`;
        if (progressText) progressText.textContent = `${Math.round(progress)}% downloaded`;
        
        if (progress >= 100) {
            setTimeout(() => {
                renderModels();
            }, 1000);
        }
    }
}

async function clearAllCache() {
    try {
        clearCacheBtn.textContent = '🗑️ Clearing...';
        clearCacheBtn.disabled = true;
        
        const response = await chrome.runtime.sendMessage({
            action: 'clearAllCache'
        });
        
        if (response.success) {
            await updateStorageInfo();
            await updateModelStatuses();
            renderModels();
        } else {
            showError(`Failed to clear cache: ${response.error}`);
        }
    } catch (error) {
        console.error('Error clearing cache:', error);
        showError('Failed to clear cache. Please try again.');
    } finally {
        clearCacheBtn.textContent = '🗑️ Clear All Cache';
        clearCacheBtn.disabled = false;
    }
}

// Utility functions
function getModelDisplayName(modelId: string): string {
    const model = availableModels.find(m => m.model_id === modelId);
    if (model) {
        return `${model.parameters} ${modelId.split('-')[0]}`;
    }
    return modelId.split('-')[0] || modelId;
}

function formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

function showError(message: string) {
    modelsError.textContent = message;
    modelsError.style.display = 'block';
    setTimeout(() => {
        modelsError.style.display = 'none';
    }, 5000);
}
